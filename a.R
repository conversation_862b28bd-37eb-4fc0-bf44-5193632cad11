# MaxDiff分析完整案例
# 以智能手机功能重要性研究为例

# 安装和加载必要的包
# install.packages(c("flipMaxDiff", "ggplot2", "dplyr", "reshape2", "RColorBrewer"))

library(flipMaxDiff)
library(ggplot2)
library(dplyr)
library(reshape2)
library(RColorBrewer)

# ============================================================================
# 第一部分：创建模拟数据集
# ============================================================================

# 定义属性（智能手机功能）
attributes <- c(
  "电池续航能力",
  "拍照质量", 
  "处理器性能",
  "屏幕显示效果",
  "存储空间",
  "外观设计",
  "价格",
  "品牌声誉",
  "系统流畅度",
  "网络连接速度"
)

# 创建MaxDiff实验设计（每个任务显示5个属性）
set.seed(123)
n_respondents <- 200
n_tasks <- 8
n_alternatives_per_task <- 5

# 生成平衡不完全区组设计
create_design <- function(attributes, n_tasks, n_alternatives_per_task) {
  n_attributes <- length(attributes)
  design <- matrix(0, nrow = n_tasks, ncol = n_alternatives_per_task)
  
  for(i in 1:n_tasks) {
    design[i, ] <- sample(1:n_attributes, n_alternatives_per_task, replace = FALSE)
  }
  return(design)
}

# 生成模拟响应数据
generate_responses <- function(design, attributes, n_respondents) {
  # 定义真实效用值（用于生成模拟数据）
  true_utilities <- c(0.8, 0.7, 0.9, 0.6, 0.5, 0.3, 1.0, 0.4, 0.8, 0.6)
  names(true_utilities) <- attributes
  
  responses <- data.frame()
  
  for(resp_id in 1:n_respondents) {
    # 为每个受访者添加随机噪声
    individual_utilities <- true_utilities + rnorm(length(attributes), 0, 0.2)
    
    for(task in 1:nrow(design)) {
      task_attributes <- design[task, ]
      task_utilities <- individual_utilities[task_attributes]
      
      # 选择效用最高的作为"最重要"
      best <- which.max(task_utilities)
      # 选择效用最低的作为"最不重要"  
      worst <- which.min(task_utilities)
      
      responses <- rbind(responses, data.frame(
        RespondentID = resp_id,
        Task = task,
        Alternative1 = task_attributes[1],
        Alternative2 = task_attributes[2],
        Alternative3 = task_attributes[3],
        Alternative4 = task_attributes[4],
        Alternative5 = task_attributes[5],
        Best = best,
        Worst = worst
      ))
    }
  }
  return(responses)
}

# 生成设计和数据
design_matrix <- create_design(attributes, n_tasks, n_alternatives_per_task)
maxdiff_data <- generate_responses(design_matrix, attributes, n_respondents)

# 查看数据结构
head(maxdiff_data)
print(paste("数据集包含", nrow(maxdiff_data), "个观测值"))

# ============================================================================
# 第二部分：数据预处理
# ============================================================================

# 转换数据格式为flipMaxDiff包可识别的格式
prepare_data <- function(data, attributes) {
  # 创建设计矩阵
  design_cols <- paste0("Alternative", 1:5)
  design_matrix <- as.matrix(data[, design_cols])
  
  # 创建最佳和最差选择矩阵
  best_matrix <- matrix(0, nrow = nrow(data), ncol = length(attributes))
  worst_matrix <- matrix(0, nrow = nrow(data), ncol = length(attributes))
  
  for(i in 1:nrow(data)) {
    best_attr <- design_matrix[i, data$Best[i]]
    worst_attr <- design_matrix[i, data$Worst[i]]
    
    best_matrix[i, best_attr] <- 1
    worst_matrix[i, worst_attr] <- 1
  }
  
  colnames(best_matrix) <- attributes
  colnames(worst_matrix) <- attributes
  
  return(list(
    design = design_matrix,
    best = best_matrix,
    worst = worst_matrix,
    respondent_id = data$RespondentID
  ))
}

processed_data <- prepare_data(maxdiff_data, attributes)

# ============================================================================
# 第三部分：计数分析法（简单方法）
# ============================================================================

# 计算每个属性的选择频次
counting_analysis <- function(processed_data, attributes) {
  best_counts <- colSums(processed_data$best)
  worst_counts <- colSums(processed_data$worst)
  net_counts <- best_counts - worst_counts
  
  results <- data.frame(
    Attribute = attributes,
    Best_Count = best_counts,
    Worst_Count = worst_counts,
    Net_Score = net_counts,
    Importance_Score = (net_counts - min(net_counts)) / (max(net_counts) - min(net_counts)) * 100
  )
  
  results <- results[order(-results$Net_Score), ]
  return(results)
}

counting_results <- counting_analysis(processed_data, attributes)
print("计数分析结果:")
print(counting_results)

# ============================================================================
# 第四部分：多项逻辑回归分析
# ============================================================================

# 准备逻辑回归数据
prepare_logit_data <- function(maxdiff_data, attributes) {
  logit_data <- data.frame()
  
  for(i in 1:nrow(maxdiff_data)) {
    task_attrs <- c(maxdiff_data$Alternative1[i], maxdiff_data$Alternative2[i], 
                   maxdiff_data$Alternative3[i], maxdiff_data$Alternative4[i], 
                   maxdiff_data$Alternative5[i])
    
    for(j in 1:length(task_attrs)) {
      choice_best <- ifelse(j == maxdiff_data$Best[i], 1, 0)
      choice_worst <- ifelse(j == maxdiff_data$Worst[i], -1, 0)
      choice <- choice_best + choice_worst
      
      if(choice != 0) {  # 只包含被选中的选项
        attr_dummy <- rep(0, length(attributes))
        attr_dummy[task_attrs[j]] <- 1
        
        row_data <- data.frame(
          RespondentID = maxdiff_data$RespondentID[i],
          Task = maxdiff_data$Task[i],
          Choice = choice,
          t(attr_dummy)
        )
        names(row_data)[4:ncol(row_data)] <- make.names(attributes)
        logit_data <- rbind(logit_data, row_data)
      }
    }
  }
  return(logit_data)
}

logit_data <- prepare_logit_data(maxdiff_data, attributes)

# 运行多项逻辑回归
formula_str <- paste("Choice ~", paste(make.names(attributes), collapse = " + "), "- 1")
logit_model <- glm(as.formula(formula_str), data = logit_data, family = gaussian())

# 提取系数
logit_coefficients <- coef(logit_model)
names(logit_coefficients) <- attributes

logit_results <- data.frame(
  Attribute = attributes,
  Coefficient = logit_coefficients,
  Relative_Importance = (logit_coefficients - min(logit_coefficients)) / 
                       (max(logit_coefficients) - min(logit_coefficients)) * 100
)
logit_results <- logit_results[order(-logit_results$Coefficient), ]

print("逻辑回归分析结果:")
print(logit_results)

# ============================================================================
# 第五部分：结果可视化
# ============================================================================

# 1. 属性重要性排序图
plot_importance <- function(results, title) {
  results$Attribute <- factor(results$Attribute, levels = results$Attribute[order(results$Relative_Importance)])
  
  ggplot(results, aes(x = Attribute, y = Relative_Importance)) +
    geom_bar(stat = "identity", fill = "steelblue", alpha = 0.7) +
    coord_flip() +
    labs(title = title,
         x = "属性",
         y = "相对重要性 (%)") +
    theme_minimal() +
    theme(text = element_text(family = "STKaiti"),  # 支持中文
          plot.title = element_text(hjust = 0.5))
}

# 绘制计数分析结果
p1 <- plot_importance(counting_results %>% 
                     select(Attribute, Relative_Importance = Importance_Score),
                     "MaxDiff分析结果 - 计数法")

# 绘制逻辑回归结果
p2 <- plot_importance(logit_results, "MaxDiff分析结果 - 逻辑回归法")

print(p1)
print(p2)

# 2. 最佳vs最差选择对比图
best_worst_plot <- function(counting_results) {
  plot_data <- counting_results %>%
    select(Attribute, Best_Count, Worst_Count) %>%
    melt(id.vars = "Attribute", variable.name = "Choice_Type", value.name = "Count")
  
  plot_data$Attribute <- factor(plot_data$Attribute, 
                               levels = counting_results$Attribute[order(counting_results$Net_Score)])
  
  ggplot(plot_data, aes(x = Attribute, y = Count, fill = Choice_Type)) +
    geom_bar(stat = "identity", position = "dodge") +
    coord_flip() +
    scale_fill_manual(values = c("Best_Count" = "green3", "Worst_Count" = "red3"),
                     labels = c("最重要", "最不重要")) +
    labs(title = "属性选择频次对比",
         x = "属性",
         y = "选择次数",
         fill = "选择类型") +
    theme_minimal() +
    theme(text = element_text(family = "STKaiti"),
          plot.title = element_text(hjust = 0.5),
          legend.position = "bottom")
}

p3 <- best_worst_plot(counting_results)
print(p3)

# ============================================================================
# 第六部分：结果汇总和解释
# ============================================================================

# 创建综合结果表
create_summary <- function(counting_results, logit_results) {
  summary_table <- data.frame(
    Attribute = attributes,
    Counting_Rank = match(attributes, counting_results$Attribute[order(-counting_results$Net_Score)]),
    Counting_Score = counting_results$Importance_Score[match(attributes, counting_results$Attribute)],
    Logit_Rank = match(attributes, logit_results$Attribute[order(-logit_results$Coefficient)]),
    Logit_Score = logit_results$Relative_Importance[match(attributes, logit_results$Attribute)]
  )
  
  summary_table$Avg_Rank <- (summary_table$Counting_Rank + summary_table$Logit_Rank) / 2
  summary_table <- summary_table[order(summary_table$Avg_Rank), ]
  
  return(summary_table)
}

final_summary <- create_summary(counting_results, logit_results)

print("=== MaxDiff分析综合结果 ===")
print(final_summary)

# 输出关键洞察
cat("\n=== 关键洞察 ===\n")
cat("最重要的前3个属性:\n")
for(i in 1:3) {
  cat(paste(i, ".", final_summary$Attribute[i], "\n"))
}

cat("\n最不重要的后3个属性:\n")
n <- nrow(final_summary)
for(i in (n-2):n) {
  cat(paste(n-i+1, ".", final_summary$Attribute[i], "\n"))
}

cat(paste("\n样本量:", n_respondents, "人"))
cat(paste("\n任务数:", n_tasks, "个"))
cat(paste("\n总观测值:", nrow(maxdiff_data), "个\n"))
